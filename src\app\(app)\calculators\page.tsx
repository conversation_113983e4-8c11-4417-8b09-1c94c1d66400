import {Play, Calculator, DollarSign, <PERSON><PERSON><PERSON> } from 'lucide-react';
import Link from 'next/link';
import React from 'react';
import { ReactElement } from 'react';

const planCalculators = [
  {
    icon: <Play className="w-6 h-6 text-white" />,
    title: "Plan 1 Calculator",
    description: "For pre-2012 students (England & Wales) and Northern Ireland students",
    badges: [
      { text: "Pre-2012", className: "bg-blue-100 text-blue-600" },
      { text: "9% Above £22,015", className: "bg-green-100 text-green-700" }
    ],
    link: "/calculators/plan-1-student-loan-calculator"
  },
  {
    icon: <Play className="w-6 h-6 text-white" />,
    title: "Plan 2 Calculator",
    description: "For 2012-2023 students in England & Wales",
    badges: [
      { text: "2012-2023", className: "bg-blue-100 text-blue-600" },
      { text: "9% Above £27,295", className: "bg-green-100 text-green-700" }
    ],
    link: "/calculators/plan-2-student-loan-calculator"
  },
  {
    icon: <Play className="w-6 h-6 text-white" />,
    title: "Plan 4 Calculator",
    description: "For students in Scotland",
    badges: [
      { text: "Scotland", className: "bg-blue-100 text-blue-600" },
      { text: "9% Above £27,660", className: "bg-green-100 text-green-700" }
    ],
    link: "/calculators/plan-4-student-loan-calculator"
  },
  {
    icon: <Play className="w-6 h-6 text-white" />,
    title: "Plan 5 Calculator",
    description: "For students starting from September 2023 in England & Wales",
    badges: [
      { text: "2023 Onwards", className: "bg-blue-100 text-blue-600" },
      { text: "9% Above £25,000", className: "bg-green-100 text-green-700" }
    ],
    link: "/calculators/plan-5-student-loan-calculator"
  },
  {
    icon: <Calculator className="w-6 h-6 text-white" />,
    title: "Postgraduate Loan Calculator",
    description: "For Master's & PhD students across the UK",
    badges: [
      { text: "Postgraduate", className: "bg-blue-100 text-blue-600" },
      { text: "6% Above £21,000", className: "bg-green-100 text-green-700" }
    ],
    link: "/calculators/postgraduate-loan-calculator"
  },
  {
    icon: <BarChart className="w-6 h-6 text-white" />,
    title: "Combined Loans Calculator",
    description: "Calculate repayments with multiple loan plans",
    badges: [
      { text: "Multiple Plans", className: "bg-purple-100 text-purple-700" },
      { text: "Simultaneous Repayments", className: "bg-pink-100 text-pink-700" }
    ],
    link: "/calculators/combined-repayment-calculator"
  }
];

const financeCalculators = [
  {
    icon: <Calculator className="w-6 h-6 text-white" />,
    title: "Monthly Repayment Calculator",
    description: "Compare monthly payments across all loan types",
    badges: [
      { text: "Budgeting", className: "bg-green-100 text-green-700" },
    ],
    link: "/calculators/monthly-repayment-calculator"
  },
  {
    icon: <DollarSign className="w-6 h-6 text-white" />,
    title: "Total Loan Cost Calculator",
    description: "Estimate lifetime repayments and if your loan will be repaid in full",
    badges: [
      { text: "Long-term Planning", className: "bg-green-100 text-green-700" }
    ],
    link: "/calculators/total-loan-cost-calculator"
  },
  {
    icon: <BarChart className="w-6 h-6 text-white" />,
    title: "Interest Calculator",
    description: "See how interest accumulates on your student loan",
    badges: [
      { text: "Interest Analysis", className: "bg-green-100 text-green-700" }
    ],
    link: "/calculators/student-loan-interest-calculator"
  },
  {
    icon: <BarChart className="w-6 h-6 text-white" />,
    title: "Overpayment Calculator",
    description: "Analyze if making extra payments is financially beneficial",
    badges: [
      { text: "Repayment Strategy", className: "bg-green-100 text-green-700" }
    ],
    link: "/calculators/student-loan-overpayment-calculator"
  },
  {
    icon: <BarChart className="w-6 h-6 text-white" />,
    title: "Write-Off Checker",
    description: "Find out when your loan will be automatically cancelled",
    badges: [
      { text: "Loan Forgiveness", className: "bg-green-100 text-green-700" }
    ],
    link: "/calculators/loan-write-off-checker"
  }
];

// function FilterDropdown({ label, value }) {
//   return (
//     <div>
//       <span className="text-xs font-semibold text-gray-500">{label}</span>
//       <button className="flex items-center w-full mt-1 px-3 py-1 border border-gray-200 bg-white rounded shadow-sm text-gray-700 font-medium min-w-[120px] justify-between">
//         {value}
//         <ChevronDown className="w-4 h-4 text-gray-400"/>
//       </button>
//     </div>
//   );
// }

interface Badge {
  text: string;
  className: string;
}

interface CardProps {
  icon: ReactElement;
  title: string;
  description: string;
  badges: Badge[];
  link: string;
}

function Card({ icon, title, description, badges, link }: CardProps) {
  return (
    <div className="rounded-xl bg-white shadow-sm px-5 py-6 flex flex-col h-full hover:shadow-lg transition-shadow duration-200 cursor-pointer">
      <Link
        href={link}
      >
       <div className="flex items-center mb-3">
        <div className="w-10 h-10 rounded-full bg-primary flex items-center justify-center">
          {/* {React.cloneElement(icon, { className: "w-5 h-5 text-white" })} */}
          {icon}
        </div>
      </div>
      <h3 className="font-semibold text-xl mb-2 text-gray-900">{title}</h3>
      <p className="text-gray-500 text-lg mb-3">{description}</p>
      <div className="flex flex-wrap gap-2">
        {badges.map((b, i) => (
          <span key={i} className={`px-2 py-1 rounded-sm text-sm font-semibold ${b.className}`}>{b.text}</span>
        ))}
      </div>
      </Link>
    </div>
  );
}

const StudentLoanCalculatorsPage = () => (
  <div className="min-h-screen bg-gray-50 py-6">
    {/* Blue header */}
    <div className="rounded-2xl bg-blue-50 px-8 py-8 container mx-auto mb-8 text-center">
      <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-1">
        Student Loan Calculators
      </h1>
      <p className="text-gray-600 text-lg">
        Choose from our range of specialised calculators to understand your UK student loan
      </p>
    </div>
    {/* Filter bar */}
    {/* <div className="bg-white rounded-xl border border-gray-200 max-w-4xl mx-auto p-6 flex flex-col md:flex-row md:items-center md:justify-between gap-6 mb-8 shadow-sm">
      <div className="font-bold text-md text-gray-800">Filter Calculators</div>
      <div className="flex flex-col md:flex-row gap-3 w-full md:w-auto">
        <FilterDropdown label="By Loan Plan" value="All Plans" />
        <FilterDropdown label="By Goal" value="All Goals" />
        <FilterDropdown label="By Audience" value="All Users" />
      </div>
    </div> */}
    {/* Plan-Specific Calculators */}
    <div className="max-w-6xl mx-auto mb-6">
      <h2 className="font-semibold text-2xl mb-4 mt-8 text-gray-800">Plan-Specific Calculators</h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-5">
        {planCalculators.map((calc, i) => <Card key={i} {...calc} />)}
      </div>
    </div>
    {/* Financial Planning Tools */}
    <div className="max-w-6xl mx-auto">
      <h2 className="font-semibold text-2xl mb-4 mt-10 text-gray-800">Financial Planning Tools</h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-5">
        {financeCalculators.map((calc, i) => <Card key={i} {...calc} />)}
      </div>
    </div>
  </div>
);

export default StudentLoanCalculatorsPage
