import Plan2CalculatorPage from '@/modules/calculator/plan-2-student-loan-calculator'
import React from 'react'

const Plan2Calculator = () => {
  return (
    <div>
        <Plan2CalculatorPage/>
    </div>
  )
}

export default Plan2Calculator

// "use client";

// import { CheckCircle, Lightbulb, Lock, ShieldCheck, VerifiedIcon } from "lucide-react";
// import Link from "next/link";
// import { useMemo, useState } from "react";

// type Results = {
//   monthlyRepaymentNow: number;
//   annualRepayment: number;
//   totalPaid: number;
//   totalInterestPaid: number;
//   cleared: boolean;
//   monthsToClear: number;
//   writeOffDate: Date;
//   appliedAnnualRate: number; // RPI..RPI+3% based on income
// };

// const GBP0 = (v: number) =>
//   v.toLocaleString("en-GB", { style: "currency", currency: "GBP", maximumFractionDigits: 0 });

// const fmtMonth = (d: Date) =>
//   d.toLocaleString("en-GB", { month: "long", year: "numeric" });

// /** PLAN 2 rules */
// const PLAN2 = {
//   threshold: 27295,
//   upperThreshold: 49130,
//   rate: 0.09,
//   writeOffYears: 30,
// } as const;

// /** Set your current RPI here (kept internal – no input) */
// const RPI = 0.046; // 4.6% example

// export default function Plan2CalculatorPage() {
//   const [salary, setSalary] = useState<number>(30000);
//   const [gradYear, setGradYear] = useState<number>(2020);
//   const [loan, setLoan] = useState<number>(45000);
//   const [includeOverpay, setIncludeOverpay] = useState(false);
//   const [overpayMonthly, setOverpayMonthly] = useState<number>(0);

//   const startDate = useMemo(() => new Date(gradYear, 3, 1), [gradYear]); // April
//   const writeOffDate = useMemo(
//     () => new Date(gradYear + PLAN2.writeOffYears, 3, 1),
//     [gradYear]
//   );

//   const results: Results = useMemo(() => {
//     const appliedAnnualRate = plan2InterestForIncome(RPI, salary);
//     const monthlyRate = appliedAnnualRate / 12;

//     const mandatoryMonthly = Math.max(0, ((salary - PLAN2.threshold) * PLAN2.rate) / 12);
//     const monthlyRepaymentNow = mandatoryMonthly + (includeOverpay ? Math.max(0, overpayMonthly) : 0);

//     let bal = Math.max(0, loan);
//     let paid = 0;
//     let interestPaid = 0;
//     let months = 0;

//     const maxMonths =
//       (writeOffDate.getFullYear() - startDate.getFullYear()) * 12 +
//       (writeOffDate.getMonth() - startDate.getMonth());

//     for (let m = 0; m < Math.max(0, maxMonths); m++) {
//       if (bal <= 0) break;

//       const interestThisMonth = bal * monthlyRate;
//       bal += interestThisMonth;

//       const repay = Math.min(monthlyRepaymentNow, bal);
//       bal -= repay;

//       paid += repay;
//       interestPaid += Math.min(repay, interestThisMonth);
//       months = m + 1;
//     }

//     const cleared = bal <= 0;

//     return {
//       monthlyRepaymentNow,
//       annualRepayment: monthlyRepaymentNow * 12,
//       totalPaid: Math.round(paid),
//       totalInterestPaid: Math.round(interestPaid),
//       cleared,
//       monthsToClear: cleared ? months : 0,
//       writeOffDate,
//       appliedAnnualRate,
//     };
//   }, [salary, loan, overpayMonthly, includeOverpay, startDate, writeOffDate]);

//   const years = Array.from({ length: 20 }, (_, i) => 2012 + i);

//   return (
//     <main className="min-h-screen bg-background">
//       <article className="container mx-auto px-4 py-10 sm:px-6 lg:px-8">
//         {/* Hero */}
//         <div className="rounded-2xl bg-blue-50 px-8 py-8 container mx-auto mb-8 text-start">
//           <div className="max-w-4xl mx-auto">
//             <div className="flex justify-between items-start mb-1">
//               <h1 className="text-3xl md:text-4xl font-bold text-gray-900">
//                 Plan 2 Student Loan Calculator
//               </h1>
//               <span className="text-sm text-gray-500">Last updated: January 15, 2024</span>
//             </div>
//             <p className="text-gray-600 text-lg mb-4">
//               Estimate your monthly and total repayments for Plan 2 student loans in England and Wales.
//             </p>
//             <div className="inline-flex items-center bg-green-100 text-green-800 text-sm font-medium px-3 py-1 rounded-full">
//               <VerifiedIcon className="w-4 h-4 mr-2" />
//               Based on official UK government repayment policy
//             </div>
//           </div>
//         </div>

//         <div className="max-w-4xl mx-auto">
//           <div className="grid gap-6 md:grid-cols-2">
//             {/* Inputs */}
//             <section className="space-y-5">
//               <div className="rounded-md bg-primary/10 p-3">
//                 <h2 className="font-semibold mb-2">About Plan 2 Loans</h2>
//                 <p className="text-sm text-dark">
//                   Plan 2 applies to students who started university in England or Wales from September 2012. You repay
//                   9% of income above £{PLAN2.threshold.toLocaleString()} per year.{" "}
//                   <Link href="/guides/loan-types-explained" className="text-primary">Learn more about loan types</Link>.
//                 </p>
//               </div>

//               <Field label="Annual Salary" help={`The threshold for Plan 2 is £${PLAN2.threshold.toLocaleString()}.`}>
//                 <CurrencyInput value={salary} onChange={setSalary} />
//               </Field>

//               <Field label="Graduation Year">
//                 <select
//                   className="w-full rounded-md border border-input bg-background px-3 py-2"
//                   value={gradYear}
//                   onChange={(e) => setGradYear(parseInt(e.target.value))}
//                 >
//                   {years.map((y) => (
//                     <option key={y} value={y}>{y}</option>
//                   ))}
//                 </select>
//                 <p className="text-sm text-muted-foreground mt-1">
//                   Repayments start in April after you graduate.
//                 </p>
//               </Field>

//               <Field label="Total Loan Amount">
//                 <CurrencyInput value={loan} onChange={setLoan} />
//                 <p className="text-sm text-muted-foreground mt-1">Include tuition fees and maintenance loans.</p>
//               </Field>

//               <div>
//                 <label className="flex items-center gap-2">
//                   <span className="font-medium">Monthly Overpayment (Optional)</span>
//                   <input
//                     type="checkbox"
//                     checked={includeOverpay}
//                     onChange={(e) => setIncludeOverpay(e.target.checked)}
//                   />
//                   <p className="text-sm text-muted-foreground -mt-1">Include</p>
//                 </label>
//                 <div>
//                   <CurrencyInput
//                     value={overpayMonthly}
//                     onChange={setOverpayMonthly}
//                     disabled={!includeOverpay}
//                   />
//                   <p className="text-sm text-muted-foreground mt-1">
//                     Additional monthly payment on top of required amount.{" "}
//                     <Link href="/guides/should-i-overpay-loan" className="text-primary">Should you overpay?</Link>
//                   </p>
//                 </div>
//               </div>

//               {/* Button kept for UX; results already update live */}
//               <button
//                 type="button"
//                 className="mt-2 w-full rounded-md bg-emerald-600 px-4 py-3 font-semibold text-white hover:bg-emerald-700"
//               >
//                 Calculate Repayments
//               </button>
//             </section>

//             {/* Summary */}
//             <section>
//               <h2 className="font-semibold mb-4">Your Repayment Summary</h2>
//               <div className="space-y-4">
//                 <SummaryRow
//                   title="Monthly Repayment"
//                   value={GBP0(Math.round(results.monthlyRepaymentNow))}
//                   sub={`${(PLAN2.rate * 100).toFixed(0)}% of income above threshold`}
//                 />
//                 <hr />
//                 <SummaryRow title="Annual Repayment" value={GBP0(Math.round(results.annualRepayment))} />
//                 <hr />
//                 <SummaryRow title="Total to be Repaid" value={GBP0(results.totalPaid)} sub="Over the life of the loan" />
//                 <hr />
//                 <SummaryRow title="Total Interest Paid" value={GBP0(results.totalInterestPaid)} />
//                 <hr />
//                 <SummaryRow
//                   title="Expected Write-Off Date"
//                   value={
//                     results.cleared
//                       ? fmtMonth(addMonths(new Date(), results.monthsToClear))
//                       : fmtMonth(results.writeOffDate)
//                   }
//                   sub={results.cleared ? "Based on current inputs, loan clears before write-off" : "30 years after entering repayment"}
//                 />

//                 <div className="rounded-md bg-primary/10 p-3">
//                   <p className="text-sm">
//                     <strong>Current Interest Rate: </strong>
//                     {rpiText(RPI, salary)} ({(results.appliedAnnualRate * 100).toFixed(1)}% total)
//                   </p>
//                 </div>

//                 <div className="rounded-2xl bg-blue-50 p-5">
//                   <h3 className="font-semibold text-gray-900 mb-3">Why Trust This Calculator?</h3>
//                   <ul className="space-y-2 text-gray-700">
//                     <li className="flex items-center gap-2">
//                       <CheckCircle className="w-5 h-5 text-blue-600" />
//                       Based on official UK loan plans
//                     </li>
//                     <li className="flex items-center gap-2">
//                       <Lock className="w-5 h-5 text-blue-600" />
//                       No personal data collected
//                     </li>
//                     <li className="flex items-center gap-2">
//                       <Lightbulb className="w-5 h-5 text-blue-600" />
//                       Calculators built by financial experts
//                     </li>
//                   </ul>
//                 </div>
//               </div>
//             </section>
//           </div>

//           {/* Footer notes */}
//           <div className="mt-10">
//             <div className="bg-gray-50 rounded-md border-l-4 border-blue-200 p-4 mb-6">
//               <p className="font-semibold text-gray-700 mb-0">Disclaimer:</p>
//               <p className="text-gray-700 text-[15px] mt-1">
//                 Calculations are estimates based on current UK student loan rules. Results may change with future policy updates.
//               </p>
//             </div>

//             <hr className="my-4" />

//             <div className="flex items-center gap-2 text-green-700 text-[15px]">
//               <ShieldCheck className="w-5 h-5" />
//               Reviewed by Michael Chen, Financial Advisor (Certified in Student Finance)
//             </div>
//           </div>
//         </div>
//       </article>
//     </main>
//   );
// }

// /* ------- helpers & inputs ------- */

// function Field({
//   label,
//   help,
//   children,
// }: {
//   label: string;
//   help?: string;
//   children: React.ReactNode;
// }) {
//   return (
//     <div className="space-y-1">
//       <label className="block text-sm font-medium">{label}</label>
//       {children}
//       {help ? <p className="text-sm text-muted-foreground">{help}</p> : null}
//     </div>
//   );
// }

// function SummaryRow({
//   title,
//   value,
//   sub,
// }: {
//   title: string;
//   value: string;
//   sub?: string;
// }) {
//   return (
//     <div className="flex items-start justify-between p-3">
//       <div>
//         <div className="text-sm text-muted-foreground">{title}</div>
//         <div className="text-2xl font-bold">{value}</div>
//         {sub ? <div className="text-sm text-muted-foreground">{sub}</div> : null}
//       </div>
//     </div>
//   );
// }

// function CurrencyInput({
//   value,
//   onChange,
//   disabled,
// }: {
//   value: number;
//   onChange: (v: number) => void;
//   disabled?: boolean;
// }) {
//   return (
//     <div className="relative">
//       <span className="pointer-events-none absolute inset-y-0 left-3 flex items-center font-medium">£</span>
//       <input
//         type="number"
//         className="w-full rounded-md border border-input bg-background px-7 py-2"
//         min={0}
//         step="1"
//         value={Number.isFinite(value) ? value : 0}
//         onChange={(e) => onChange(safeNumber(e.target.value))}
//         disabled={disabled}
//       />
//     </div>
//   );
// }

// /* ------- utils & interest ------- */

// function safeNumber(v: string) {
//   const n = Number(v);
//   return Number.isFinite(n) ? n : 0;
// }

// function addMonths(date: Date, months: number) {
//   const d = new Date(date);
//   d.setMonth(d.getMonth() + months);
//   return d;
// }

// /** Sliding Plan 2 interest (post-study): RPI .. RPI+3% depending on income */
// function plan2InterestForIncome(rpi: number, income: number) {
//   if (income <= PLAN2.threshold) return rpi; // RPI
//   if (income >= PLAN2.upperThreshold) return rpi + 0.03; // RPI + 3%
//   const t = (income - PLAN2.threshold) / (PLAN2.upperThreshold - PLAN2.threshold);
//   return rpi + 0.03 * t; // linear between thresholds
// }

// function rpiText(rpi: number, income: number) {
//   if (income <= PLAN2.threshold) return `RPI only (${(rpi * 100).toFixed(1)}%)`;
//   if (income >= PLAN2.upperThreshold) return `RPI + 3%`;
//   return `RPI → RPI+3% (sliding)`;
// }
