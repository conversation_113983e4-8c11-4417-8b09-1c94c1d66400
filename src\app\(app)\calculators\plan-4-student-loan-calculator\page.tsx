import Plan4CalculatorPage from '@/modules/calculator/plan-4-student-loan-calculator'
import React from 'react'

const Plan4Calculator = () => {
  return (
    <div>
        <Plan4CalculatorPage/>
    </div>
  )
}

export default Plan4Calculator


// "use client";
// import { CheckCircle, Lightbulb, Lock, ShieldCheck, VerifiedIcon } from "lucide-react";
// import type { Metadata } from "next";
// import Link from "next/link";
// import { useMemo, useState } from "react";

// // export const metadata: Metadata = {
// //   title: "Plan 1 Student Loan Calculator (UK) | Estimate Monthly & Total Repayments",
// //   description:
// //     "Interactive Plan 1 student loan calculator for England, Wales (pre-2012) and Northern Ireland. Estimate monthly repayments, interest, and write-off date with optional overpayments.",
// // };

// type Results = {
//     monthlyRepaymentNow: number;
//     annualRepayment: number;
//     totalPaid: number;
//     totalInterestPaid: number;
//     cleared: boolean;
//     monthsToClear: number;
//     writeOffDate: Date;
// };

// const GBP = (v: number) =>
//     v.toLocaleString("en-GB", { style: "currency", currency: "GBP", maximumFractionDigits: 0 });

// const fmtMonth = (d: Date) =>
//     d.toLocaleString("en-GB", { month: "long", year: "numeric" });

// /**
//  * Core assumptions (kept explicit and easy to change):
//  * - Plan 1 threshold: £22,015 (2024/25)
//  * - Repay 9% of income above threshold
//  * - Interest: nominal annual rate (default 4.6%) applied monthly, simple compounding
//  * - Repayments start April after graduation year
//  * - Write-off: 25 years after entering repayment
//  * - Salary held constant; no threshold indexation in the simulation
//  */
// const PLAN1 = {
//     threshold: 27660,
//     rate: 0.09,
//     writeOffYears: 25,
// };

// export default function Plan4CalculatorPage() {
//     const [salary, setSalary] = useState<number>(30000);
//     const [gradYear, setGradYear] = useState<number>(2010);
//     const [loan, setLoan] = useState<number>(15000);
//     const [interestAnnual, setInterestAnnual] = useState<number>(0.046); // 4.6% default
//     const [includeOverpay, setIncludeOverpay] = useState(false);
//     const [overpayMonthly, setOverpayMonthly] = useState<number>(0);

//     const startDate = useMemo(() => new Date(gradYear, 3, 1), [gradYear]); // April = month index 3
//     const writeOffDate = useMemo(
//         () => new Date(gradYear + PLAN1.writeOffYears, 3, 1),
//         [gradYear]
//     );

//     const results: Results = useMemo(() => {
//         const monthlyRate = interestAnnual / 12;
//         const monthlyBase = Math.max(0, ((salary - PLAN1.threshold) * PLAN1.rate) / 12);
//         const monthlyRepaymentNow = monthlyBase + (includeOverpay ? Math.max(0, overpayMonthly) : 0);

//         // Simulate month by month from start to write-off
//         let bal = Math.max(0, loan);
//         let paid = 0;
//         let interestPaid = 0;
//         let months = 0;

//         // Stop when written off, or cleared (cap iterations for safety)
//         const maxMonths =
//             (writeOffDate.getFullYear() - startDate.getFullYear()) * 12 +
//             (writeOffDate.getMonth() - startDate.getMonth());

//         for (let m = 0; m < Math.max(0, maxMonths); m++) {
//             if (bal <= 0) break;

//             // Interest accrues on outstanding balance
//             const interestThisMonth = bal * monthlyRate;
//             bal += interestThisMonth;

//             // Repayment for the month
//             const repay = Math.min(monthlyRepaymentNow, bal);
//             bal -= repay;

//             paid += repay;
//             // Only the portion of repayment that covers interest counts as "interest paid".
//             // If repay <= interest, all of it is effectively interest.
//             const interestComponent = Math.min(repay, interestThisMonth);
//             interestPaid += interestComponent;

//             months = m + 1;
//         }

//         const cleared = bal <= 0;
//         return {
//             monthlyRepaymentNow,
//             annualRepayment: monthlyRepaymentNow * 12,
//             totalPaid: Math.round(paid),
//             totalInterestPaid: Math.round(interestPaid),
//             cleared,
//             monthsToClear: cleared ? months : 0,
//             writeOffDate,
//         };
//     }, [
//         salary,
//         loan,
//         interestAnnual,
//         overpayMonthly,
//         includeOverpay,
//         startDate,
//         writeOffDate,
//     ]);

//     const years = Array.from({ length: 20 }, (_, i) => 2006 + i);

//     return (
//         <main className="min-h-screen bg-background">
//             <article className="container mx-auto px-4 py-10 sm:px-6 lg:px-8">
//                 <div className=" rounded-2xl bg-blue-50 px-8 py-8 container mx-auto mb-8 text-start relative">
//                     <div className="max-w-4xl mx-auto">
//                         <div className="flex justify-between">
//                             <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-1">
//                                 Plan 4 Student Loan Calculator
//                             </h1>
//                         </div>

//                         <p className="text-gray-600 text-lg mb-6">
//                             Estimate your monthly and total repayments for Plan 4 student loans in Scotland.
//                         </p>
//                     </div>
//                 </div>

//                 <div className="max-w-4xl mx-auto">
//                     <div className="grid gap-6 md:grid-cols-2">
//                         <section className="space-y-5">
//                             <div className="rounded-md  bg-primary/10 p-3">
//                                 <h2 className="font-semibold mb-2">About Plan 4 Loans</h2>
//                                 <p className="text-sm text-dark">
//                                     Plan 4 loans apply to students who started university in Scotland after September 1998. You repay 9% of income above £27,660 per year.
//                                 </p>
//                             </div>

//                             <Field label="Annual Salary" help={`The threshold for Plan 2 is £${PLAN1.threshold.toLocaleString()}.`}>
//                                 <CurrencyInput value={salary} onChange={setSalary} />
//                             </Field>

//                             <Field label="Graduation Year">
//                                 <select
//                                     className="w-full rounded-md border border-input bg-background px-3 py-2"
//                                     value={gradYear}
//                                     onChange={(e) => setGradYear(parseInt(e.target.value))}
//                                 >
//                                     {years.map((y) => (
//                                         <option key={y} value={y}>
//                                             {y}
//                                         </option>
//                                     ))}
//                                 </select>
//                                 <p className="text-sm text-muted-foreground mt-1">
//                                     Plan 4 applies to Scottish students
//                                 </p>
//                             </Field>

//                             <Field label="Total Loan Amount">
//                                 <CurrencyInput value={loan} onChange={setLoan} />
//                                 <p className="text-sm text-muted-foreground mt-1">
//                                     Scottish students typically have lower tuition fees but may have maintenance loans
//                                 </p>
//                             </Field>

//                             <div className="">
//                                 <label className="flex items-center gap-2">
//                                     <span className="font-medium">Monthly Overpayment (Optional)</span>
//                                     <input
//                                         type="checkbox"
//                                         checked={includeOverpay}
//                                         onChange={(e) => setIncludeOverpay(e.target.checked)}
//                                     />
//                                     <p className="text-sm text-muted-foreground -mt-1">
//                                         Include
//                                     </p>
//                                 </label>
//                                 <div className="">
//                                     <CurrencyInput
//                                         value={overpayMonthly}
//                                         onChange={setOverpayMonthly}
//                                         disabled={!includeOverpay}
//                                     />
//                                     <p className="text-sm text-muted-foreground mt-1">
//                                         Additional monthly payment on top of required amount
//                                     </p>
//                                 </div>
//                             </div>
//                             <button
//                                 className="mt-6 w-full rounded-md bg-emerald-600 px-4 py-3 font-semibold text-white hover:bg-emerald-700"
//                                 onClick={() => window.scrollTo({ top: document.body.scrollHeight, behavior: "smooth" })}
//                             >
//                                 Calculate Repayments
//                             </button>
//                         </section>

//                         <section className="">
//                             <h2 className="font-semibold mb-4">Your Repayment Summary</h2>
//                             <div className="space-y-1">
//                                 <SummaryRow
//                                     title="Monthly Repayment"
//                                     value={GBP(Math.round(results.monthlyRepaymentNow))}
//                                     sub={`${(PLAN1.rate * 100).toFixed(0)}% of income above threshold`}
//                                 />
//                                 <hr className="" />
//                                 <SummaryRow title="Annual Repayment" value={GBP(Math.round(results.annualRepayment))} />
//                                 <hr className="" />
//                                 <SummaryRow title="Total to be Repaid" value={GBP(results.totalPaid)} sub="Over the life of the loan" />
//                                 <hr className="" />
//                                 <SummaryRow title="Total Interest Paid" value={GBP(results.totalInterestPaid)} />
//                                 <hr className="" />
//                                 <SummaryRow
//                                     title="Expected Write-Off Date"
//                                     value={
//                                         results.cleared
//                                             ? fmtMonth(addMonths(new Date(), results.monthsToClear))
//                                             : fmtMonth(results.writeOffDate)
//                                     }
//                                     sub={results.cleared ? "Based on current inputs, loan clears before write-off" : "30 years after graduation"}
//                                 />
//                                 <div className="rounded-md  bg-primary/10 p-3">
//                                     <p className="text-sm text-dark">
//                                         <strong >Current Interest Rate: </strong>Lower of RPI or Base Rate +1% (currently 4.6% total)
//                                     </p>
//                                 </div>
//                             </div>
//                         </section>
//                     </div>
//                 </div>
//             </article>
//         </main>
//     );
// }


// function Field({
//     label,
//     help,
//     children,
// }: {
//     label: string;
//     help?: string;
//     children: React.ReactNode;
// }) {
//     return (
//         <div className="space-y-1">
//             <label className="block text-sm font-medium">{label}</label>
//             {children}
//             {help ? <p className="text-sm text-muted-foreground">{help}</p> : null}
//         </div>
//     );
// }

// function SummaryRow({
//     title,
//     value,
//     sub,
// }: {
//     title: string;
//     value: string;
//     sub?: string;
// }) {
//     return (
//         <div className="flex items-start justify-between p-3">
//             <div>
//                 <div className="text-sm text-muted-foreground">{title}</div>
//                 <div className="text-2xl font-bold">{value}</div>
//                 {sub ? <div className="text-sm text-muted-foreground">{sub}</div> : null}
//             </div>

//         </div>
//     );
// }

// function CurrencyInput({
//     value,
//     onChange,
//     disabled,
// }: {
//     value: number;
//     onChange: (v: number) => void;
//     disabled?: boolean;
// }) {
//     return (
//         <div className="relative">
//             <span className="pointer-events-none absolute inset-y-0 left-3 flex items-center font-medium">
//                 £
//             </span>
//             <input
//                 type="number"
//                 className="w-full rounded-md border border-input bg-background px-7 py-2"
//                 min={0}
//                 step="1"
//                 value={Number.isFinite(value) ? value : 0}
//                 onChange={(e) => onChange(safeNumber(e.target.value))}
//                 disabled={disabled}
//             />
//         </div>
//     );
// }

// function safeNumber(v: string) {
//     const n = Number(v);
//     return Number.isFinite(n) ? n : 0;
// }

// function addMonths(date: Date, months: number) {
//     const d = new Date(date);
//     d.setMonth(d.getMonth() + months);
//     return d;
// }
