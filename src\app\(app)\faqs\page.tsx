import {
  Accordion,
  Accordion<PERSON><PERSON>,
  Accordion<PERSON><PERSON>ger,
  AccordionContent,
} from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import Link from "next/link";

const faqData = [
  {
    section: "Basics of Student Loans",
    items: [
      { q: "Do I have to pay my student loan back?", a: "Yes. UK student loans must be repaid unless written off/cancelled after a period or on death/disability. See your plan terms for full details." },
      { q: "When do I start repaying my student loan?", a: "You start repaying from the April after you leave your course, if your income is above the threshold for your plan type." },
      { q: "How much can I borrow for my student loan?", a: "This depends on your year, course, residence, and household income. See gov.uk for current figures." },
      { q: "What is the difference between a tuition fee loan and a maintenance loan?", a: "Tuition loans pay fees to your university; maintenance loans help with living costs. Both are repaid together." },
    ],
  },
  {
    section: "Repayments",
    items: [
      { q: "How much will I pay back each month?", a: "You pay a percent (usually 9%) of your income above the plan threshold each month. Check your plan for the rate and threshold." },
      { q: "How are student loan repayments calculated?", a: "It's a percentage of your income above the threshold, not the loan amount or total owed." },
      { q: "Can I make extra repayments to clear my student loan faster?", a: "Yes, you can make voluntary extra repayments at any time via your loan account portal." },
      { q: "What happens if I go abroad after graduation?", a: "You’re still required to repay your UK student loan. You must update SLC about your income overseas to avoid penalty charges." },
    ],
  },
  {
    section: "Interest and Financial Impact",
    items: [
      { q: "What is the interest rate on student loans?", a: "Interest rates depend on your plan and RPI/base rate. See the Student Loan Company website for current figures by plan." },
      { q: "Does my student loan affect my credit score?", a: "No, UK student loans do not appear on your credit file, but repayments can affect your take-home pay." },
      { q: "Will having a student loan affect my ability to get a mortgage?", a: "Student loans do not directly stop you getting a mortgage, but repayments lower your net income and may affect affordability checks." },
      { q: "Should I pay off my student loan early?", a: "This depends on your income prospects and plan type; early repayment can save interest but isn’t always optimal. See our guides." },
    ],
  },
  {
    section: "Special Circumstances",
    items: [
      { q: "What happens to my student loan if I drop out of university?", a: "You still owe any loan paid while you were enrolled. Repayment terms remain similar." },
      { q: "How do student loans work if I'm self-employed?", a: "You declare earnings through self-assessment and repay via HMRC if earning over the threshold." },
      { q: "What happens to my loan repayments if I take maternity/paternity leave?", a: "Repayment is based on your actual income. If it falls below the threshold, repayments stop automatically." },
      { q: "What happens to my student loan if I die?", a: "Outstanding student loan debt is cancelled if you die before repaying in full, with proof required." },
    ],
  },
];

const faqSections = [
  "Basics of Student Loans",
  "Repayments",
  "Interest and Financial Impact",
  "Special Circumstances",
];

const UKStudentLoanFAQPage = () => (
  <div className="min-h-screen bg-gray-50 pb-16">
    {/* Header & Breadcrumbs */}

    <div className="rounded-2xl bg-blue-50 px-8 py-8 container mx-auto mb-8 text-center">
      <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-1">
        UK Student Loan FAQs
      </h1>
      <p className="text-gray-600 text-lg">
        Find answers to common questions about student loans, repayments, and finances.
      </p>
    </div>

    {/* Short intro and jump links */}
    {/* <div className="container mx-auto mb-4 px-4">
      <p className="text-gray-600 text-[15px] mb-3">
        We’ve compiled answers to the most frequently asked questions about UK student loans. If you can’t find the answer you’re looking for, feel free to contact us or use our specific calculator tools to understand your individual situation.
      </p>
      <div className="bg-white border border-gray-100 rounded-lg p-3 flex flex-wrap gap-2 justify-start mb-5">
        {faqSections.map(section => (
          <a key={section} href={`#${section.replace(/\s+/g, '').toLowerCase()}`} className="px-3 py-1 bg-blue-50 rounded text-blue-700 text-sm font-medium hover:bg-blue-100 focus:outline-none transition">
            {section}
          </a>
        ))}
      </div>
    </div> */}
    <div className="max-w-5xl mx-auto mb-4 px-4">
      <p className="text-gray-600 text-xl mb-3">
        We've compiled answers to the most frequently asked questions about UK student loans. If you can't find the answer you're looking for, feel free to <Link href="/contact"><span className="text-blue-700 underline">contact us</span></Link> or use our specific <Link href="/calculators"><span className="text-blue-700 underline">calculator tools</span></Link> to understand your individual situation.
      </p>
    </div>

    <div className="max-w-5xl mx-auto px-4 space-y-8">
      {faqData.map(({ section, items }) => (
        <div key={section} id={section.replace(/\s+/g, '').toLowerCase()}>
          <h2 className="font-bold text-base md:text-2xl mb-2 text-gray-900">{section}</h2>
          <div className="rounded-xl border border-gray-100 bg-white divide-y">
            <Accordion type="multiple">
              {items.map(({ q, a }, idx) => (
                <AccordionItem key={q} value={q}>
                  <AccordionTrigger className="font-medium text-lg px-4 py-4 text-gray-800 hover:bg-gray-50 transition">{q}</AccordionTrigger>
                  <AccordionContent className="text-gray-600 text-base px-4 pb-4">{a}</AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>
        </div>
      ))}
    </div>

    {/* CTA Box */}
    <div className="max-w-5xl mx-auto mt-10 px-2">
      <div className="rounded-2xl bg-gradient-to-r from-blue-500 to-blue-600 px-6 py-8 text-white text-center shadow">
        <div className="text-2xl font-bold mb-2">Need More Specific Information?</div>
        <div className="mb-4 text-xl">
          Use our calculators to understand your specific repayment situation or read our in-depth guides about student finance.
        </div>
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Button variant="outline" className="bg-white text-blue-700 border border-white font-semibold px-4 py-2 hover:bg-blue-50 transition">Try Our Calculators</Button>
          <Button className="bg-blue-800 text-white px-4 py-2 font-semibold hover:bg-blue-900 transition">Read Our Guides</Button>
        </div>
      </div>
    </div>
  </div>
);

export default UKStudentLoanFAQPage;
