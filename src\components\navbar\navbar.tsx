"use client";
import Link from "next/link";
import { usePathname } from "next/navigation";
import React, { useState } from "react";
import { Menu, X } from "lucide-react";

const NAV_LINKS = [
  { href: "/", label: "Home" },
  { href: "/calculators", label: "Calculators" },
  { href: "/plans", label: "Plans" },
  { href: "/guides", label: "Guides" },
  { href: "/faqs", label: "FAQs" },
];

const Navbar = () => {
  const pathname = usePathname();
  const [mobileOpen, setMobileOpen] = useState(false);

  const toggleMobileMenu = () => setMobileOpen(!mobileOpen);

  const renderLink = (href: string, label: string) => {
    const isActive =
      href === "/"
        ? pathname === "/"
        : pathname.startsWith(href) && href !== "/";
    return (
      <Link
        href={href}
        className={`relative px-1 py-2 font-medium transition-colors duration-200 ${
          isActive ? "text-primary" : "text-gray-600 hover:text-gray-900"
        } block md:inline`}
        onClick={() => setMobileOpen(false)}
      >
        {label}
        <span
          className={`absolute left-0 right-0 -bottom-0.5 h-[2.5px] rounded bg-primary transition-all duration-200 ${
            isActive ? "w-full opacity-100" : "w-0 opacity-0"
          }`}
        />
      </Link>
    );
  };

  return (
    <header className="border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50">
      <div className="container mx-auto px-4 py-4 flex items-center justify-between md:justify-between">
        <div className="flex items-center gap-2">
          <span className="text-xl font-bold text-primary">Student Loan Calculator</span>
        </div>

        {/* Desktop menu */}
        <nav className="hidden md:flex items-center gap-6 text-sm">
          {NAV_LINKS.map(({ href, label }) => (
            <React.Fragment key={href}>{renderLink(href, label)}</React.Fragment>
          ))}
        </nav>

        {/* Mobile toggle */}
        <button
          aria-label="Toggle menu"
          onClick={toggleMobileMenu}
          className="md:hidden focus:outline-none"
        >
          {mobileOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
        </button>
      </div>

      {/* Mobile menu */}
      {mobileOpen && (
        <nav className="md:hidden border-t border-gray-100 bg-white shadow-lg">
          <div className="flex flex-col px-4 py-4 space-y-2">
            {NAV_LINKS.map(({ href, label }) => (
              <React.Fragment key={href}>{renderLink(href, label)}</React.Fragment>
            ))}
          </div>
        </nav>
      )}
    </header>
  );
};

export default Navbar;
