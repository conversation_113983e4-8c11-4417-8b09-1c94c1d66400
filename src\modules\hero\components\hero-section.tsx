import { Button } from '@/components/ui/button'
import { Calculator } from 'lucide-react'
import React from 'react'

const EnvelopeIcon = () => (
  <svg className="w-40 h-40" fill="none" stroke="white" viewBox="0 0 24 24">
    <rect x="5" y="8" width="14" height="12" rx="2" stroke="white" strokeWidth="1.5" fill="transparent" />
    <polyline points="5,9 12,14 19,9" stroke="white" strokeWidth="1.5" fill="transparent" />
  </svg>
)

const HeroSection = () => {
  return (
    <section className="container mx-auto px-4 py-12 md:py-16">
      <div className="bg-primary rounded-3xl p-8 md:p-12 lg:p-16 text-white relative overflow-hidden">
        <div className="flex items-center justify-between">
          <div className="max-w-[500px]">
            <div className="flex items-center gap-2 mb-4">
              <div className="w-8 h-8 bg-secondary rounded-lg flex items-center justify-center">
                <Calculator className="w-5 h-5 text-white" />
              </div>
              <span className="text-base font-medium">Student Loan Calculator UK</span>
            </div>

            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-4 ">
              Understand Your UK Student Loan Repayments
            </h1>

            <p className="text-lg md:text-xl text-blue-100 mb-2">
              Trusted tools, expert guides, and calculators built for UK students and graduates.
            </p>

            <p className="text-sm text-blue-200 mb-8">
              No login required. Updated for 2025 UK loan rules
            </p>

            <div className="flex flex-wrap gap-4">
              <Button className="bg-secondary hover:bg-secondary hover:scale-105 text-xl text-dark font-semibold px-9 py-8">
                Start Calculating →
              </Button>
              <Button
                variant="outline"
                className="bg-transparent border-2 px-9 py-8 text-xl border-white text-white hover:text-white hover:bg-white/10"
              >
                Explore Guides →
              </Button>
            </div>
          </div>

          <div className="hidden lg:block">
            <div className="w-80 h-80 rounded-full border-4 border-blue-300 flex items-center justify-center bg-transparent">
              <div className="w-56 h-56 rounded-2xl bg-blue-600 shadow-lg flex items-center justify-center">
                <EnvelopeIcon />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default HeroSection
